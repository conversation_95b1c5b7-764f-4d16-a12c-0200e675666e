<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Observer for tool_sendpassmail.
 *
 * @package    tool_sendpassmail
 * @copyright  2025 RAPHAEL ENES <<EMAIL>>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

namespace tool_sendpassmail;

defined('MOODLE_INTERNAL') || die();

/**
 * Event observer for tool_sendpassmail.
 *
 * @package    tool_sendpassmail
 * @copyright  2025 RAPHAEL ENES <<EMAIL>>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class observer {
    /**
     * Handle the course_completed event.
     *
     * @param \core\event\course_completed $event The event.
     */
    public static function course_completed(\core\event\course_completed $event) {
        global $DB;

        $courseid = $event->courseid;
        $userid = $event->relateduserid;

        // Recupera os e-mails cadastrados para o courseid
        $emails = $DB->get_records('tool_sendpassmail_emails', ['courseid' => $courseid]);

        if (!empty($emails)) {
            $user = $DB->get_record('user', ['id' => $userid]);
            $course = $DB->get_record('course', ['id' => $courseid]);
            foreach ($emails as $email) {
                self::send_email($user, $course, $email->email);
            }
        }
    }

    /**
     * Send an email to the specified address.
     *
     * @param \stdClass $user The user who completed the course.
     * @param \stdClass $course The course that was completed.
     * @param string $toemail The email address to send the message to.
     */
    private static function send_email($user, $course, $toemail) {
        // Prepara o assunto e a mensagem do e-mail
        $subject = "Usuário concluiu o curso: {$course->fullname}";
        $message = "O usuário {$user->firstname} {$user->lastname} concluiu o curso {$course->fullname}.";

        $fromuser = \core_user::get_noreply_user();

        // Configura o destinatário
        $recipient = (object) [
            'email' => $toemail,
            'id' => -1, // Temporary ID for external email recipient.
            'firstname' => $user->firstname,
            'lastname' => $user->lastname,
        ];

        // Envia o e-mail
        email_to_user($recipient, $fromuser, $subject, $message);


    }
}