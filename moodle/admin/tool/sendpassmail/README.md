# Send Password Mail Plugin

## Descrição

O plugin **Send Password Mail** (tool_sendpassmail) é uma ferramenta administrativa para Moodle que permite configurar endereços de e-mail para receber notificações automáticas quando usuários completam cursos.

## Funcionalidades

- **Configuração de E-mails por Curso**: Permite adicionar múltiplos endereços de e-mail para cada curso
- **Notificações Automáticas**: Envia e-mails automaticamente quando um usuário completa um curso
- **Controle de Acesso por Papéis**: Administradores podem configurar quais papéis de curso têm acesso ao plugin
- **Interface Intuitiva**: Interface simples para gerenciar e-mails de notificação
- **Conformidade com GDPR**: Implementa provider de privacidade para conformidade com regulamentações

## Instalação

1. Copie o plugin para o diretório `admin/tool/sendpassmail/` do seu Moodle
2. Acesse a página de administração do Moodle
3. Complete o processo de instalação seguindo as instruções na tela
4. Configure os papéis permitidos em **Administração do Site > Plugins > Ferramentas Administrativas > Send Password Mail**

## Configuração

### Configuração de Papéis Permitidos

1. Vá para **Administração do Site > Plugins > Ferramentas Administrativas > Send Password Mail**
2. Selecione os papéis de curso que devem ter acesso ao plugin
3. Salve as configurações

### Configuração de E-mails por Curso

1. Entre em um curso como usuário com papel permitido
2. No menu de navegação do curso, clique em "Send Password Mail"
3. Adicione os endereços de e-mail que devem receber notificações
4. Gerencie a lista de e-mails conforme necessário

## Capacidades

O plugin define duas capacidades:

- **tool/sendpassmail:view**: Permite visualizar o plugin no curso
- **tool/sendpassmail:manage**: Permite gerenciar (adicionar/remover) e-mails de notificação

## Eventos Observados

O plugin observa o evento `\core\event\course_completed` e envia automaticamente e-mails para todos os endereços configurados para aquele curso.

## Estrutura do Banco de Dados

### Tabela: tool_sendpassmail_emails

| Campo | Tipo | Descrição |
|-------|------|-----------|
| id | int(10) | ID único do registro |
| email | varchar(255) | Endereço de e-mail |
| userid | int(10) | ID do usuário que adicionou o e-mail |
| courseid | int(10) | ID do curso |
| timecreated | int(10) | Timestamp de criação |

## Privacidade

O plugin implementa o subsistema de privacidade do Moodle e:
- Exporta dados pessoais quando solicitado
- Remove dados pessoais quando solicitado
- Declara quais dados pessoais são armazenados

## Requisitos

- Moodle 4.2 ou superior
- PHP 7.4 ou superior

## Suporte

Para suporte técnico ou relatório de bugs, entre em contato com o desenvolvedor.

## Licença

Este plugin é licenciado sob a GNU GPL v3 ou posterior.

## Autor

**RAPHAEL ENES** <<EMAIL>>

## Changelog

### Versão 1.1 (2025-01-28)
- Adicionado controle de acesso baseado em papéis
- Implementado sistema de capacidades
- Adicionado provider de privacidade para conformidade com GDPR
- Melhorada documentação e comentários do código
- Corrigidas questões de estilo de código para conformidade com padrões Moodle

### Versão 1.0 (2025-01-28)
- Versão inicial do plugin
- Funcionalidade básica de envio de e-mails em conclusão de curso
