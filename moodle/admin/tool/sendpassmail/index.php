<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * TODO describe file index
 *
 * @package    tool_sendpassmail
 * @copyright  2025 RAPHAEL ENES <<EMAIL>>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

require_once '../../../config.php';
require_once __DIR__ . '/sendpassmail_form.php';

$courseid = optional_param('courseid', 0, PARAM_INT); // courseid da URL
$deleteid = optional_param('deleteid', 0, PARAM_INT); // ID do registro a ser excluído

require_login();

if ($courseid != 0) {
    // Verifica se o curso existe
    if (!$course = $DB->get_record('course', ['id' => $courseid], '*')) {
        throw new moodle_exception('invalidcourseid', 'tool_sendpassmail');
    }
    require_login($course);
    $context = context_course::instance($courseid);
} else {
    $context = context_system::instance();
}

// Check if user has permission to view this plugin.
require_capability('tool/sendpassmail:view', $context);

// Additional check for role-based access.
$allowedroles = get_config('tool_sendpassmail', 'allowedroles');
if (!empty($allowedroles)) {
    $allowedrolesarray = explode(',', $allowedroles);
    $userroles = get_user_roles($context, $USER->id, false);
    $userrolenames = [];
    foreach ($userroles as $role) {
        $userrolenames[] = $role->roleid;
    }

    $hasallowedrole = false;
    foreach ($userrolenames as $userroleid) {
        if (in_array($userroleid, $allowedrolesarray)) {
            $hasallowedrole = true;
            break;
        }
    }

    if (!$hasallowedrole) {
        throw new moodle_exception('nopermissions', 'error', '', get_string('sendpassmail:view', 'tool_sendpassmail'));
    }
}

// URL base da página, sempre incluindo o courseid
$url = new moodle_url('/admin/tool/sendpassmail/index.php', ['courseid' => $courseid]);
$PAGE->set_context($context);
$PAGE->set_url($url);
$PAGE->set_heading(get_string('pluginname', 'tool_sendpassmail'));
$PAGE->set_title(get_string('pluginname', 'tool_sendpassmail'));

// Ação de exclusão
if ($deleteid) {
    // Check if user has permission to manage emails.
    require_capability('tool/sendpassmail:manage', $context);

    // Verify that the email record belongs to this course.
    $emailrecord = $DB->get_record('tool_sendpassmail_emails', ['id' => $deleteid]);
    if ($emailrecord && $emailrecord->courseid == $courseid) {
        $DB->delete_records('tool_sendpassmail_emails', ['id' => $deleteid]);
    }
    redirect($url); // Redireciona mantendo o courseid na URL
}

// Instancia o formulário
$form = new tool_sendpassmail_form();

// Processamento do formulário
if ($data = $form->get_data()) {
    // Check if user has permission to manage emails.
    require_capability('tool/sendpassmail:manage', $context);

    try {
        $record = new stdClass();
        $record->email = $data->email;
        $record->userid = $data->userid; // userid do usuário logado
        $record->courseid = $data->courseid; // courseid do curso atual
        $record->timecreated = time();

        // Inserir o registro no banco de dados
        $DB->insert_record('tool_sendpassmail_emails', $record);

        // Redirecionar para a mesma página após a inserção
        redirect($url);
    } catch (Exception $e) {
        // Em caso de erro, exibir uma mensagem de erro e manter o usuário na mesma página
        \core\notification::error(get_string('errorinsertingrecord', 'tool_sendpassmail'));
    }
}

echo $OUTPUT->header();

// Exibir o formulário
$form->display();

// Exibir a tabela de e-mails
$emails = $DB->get_records('tool_sendpassmail_emails', ['courseid' => $courseid]);

if (!empty($emails)) {
    $table = new html_table();
    $table->head = [
        get_string('email', 'tool_sendpassmail'),
        get_string('userid', 'tool_sendpassmail'),
        get_string('courseid', 'tool_sendpassmail'),
        get_string('action', 'tool_sendpassmail')
    ];

    foreach ($emails as $email) {
        // Link de exclusão, incluindo o courseid na URL
        $deleteurl = new moodle_url('/admin/tool/sendpassmail/index.php', [
            'deleteid' => $email->id,
            'courseid' => $courseid // Mantém o courseid na URL
        ]);
        $deleteaction = $OUTPUT->action_icon($deleteurl, new pix_icon('t/delete', get_string('delete')));
        $table->data[] = [
            $email->email,
            $email->userid,
            $email->courseid,
            $deleteaction
        ];
    }

    echo html_writer::table($table);
} else {
    echo $OUTPUT->notification(get_string('noemails', 'tool_sendpassmail'));
}

echo $OUTPUT->footer();