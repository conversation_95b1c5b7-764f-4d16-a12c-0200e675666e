<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Callback implementations for Send Pass Email
 *
 * @package    tool_sendpassmail
 * @copyright  2025 RAPHAEL ENES <<EMAIL>>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

 use tool_sendpassmail\helper;

/**
 * This function extends the navigation with the report items
 *
 * @param navigation_node $navigation The navigation node to extend
 * @param stdClass $course The course to object for the report
 * @param context $context The context of the course
 * @throws coding_exception
 * @throws moodle_exception
 */
function tool_sendpassmail_extend_navigation_course(\navigation_node $navigation, \stdClass $course, \context $context) {
    global $USER;

    // Get course context for capability checks.
    $coursecontext = context_course::instance($course->id);

    // Check if user has the capability to view the plugin.
    if (!has_capability('tool/sendpassmail:view', $coursecontext)) {
        return;
    }

    // Get the allowed roles configuration.
    $allowedroles = get_config('tool_sendpassmail', 'allowedroles');

    // If no roles are configured, don't show the plugin to anyone.
    if (empty($allowedroles)) {
        return;
    }

    // Convert the configuration string to an array.
    $allowedrolesarray = explode(',', $allowedroles);

    // Get user's roles in this course.
    $coursecontext = context_course::instance($course->id);
    $userroles = get_user_roles($coursecontext, $USER->id, false);
    $userrolenames = [];
    foreach ($userroles as $role) {
        $userrolenames[] = $role->roleid;
    }

    // Check if user has any of the allowed roles.
    $hasallowedrole = false;
    foreach ($userrolenames as $userroleid) {
        if (in_array($userroleid, $allowedrolesarray)) {
            $hasallowedrole = true;
            break;
        }
    }

    // Only add navigation if user has an allowed role.
    if ($hasallowedrole) {
        $url = new moodle_url(helper::get_plugin_url(), ['courseid' => $course->id]);
        $navigation->add(
            get_string('pluginname', 'tool_sendpassmail'),
            $url,
            navigation_node::TYPE_SETTING,
            null,
            null,
            new pix_icon('i/report', '')
        );
    }
}