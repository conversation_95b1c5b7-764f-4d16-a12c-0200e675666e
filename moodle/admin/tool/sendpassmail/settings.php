<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mo<PERSON><PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Settings for the Send Password Mail plugin.
 *
 * @package    tool_sendpassmail
 * @copyright  2025 RAPHAEL ENES <<EMAIL>>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

if ($hassiteconfig) {
    // Create the settings page.
    $settings = new admin_settingpage('tool_sendpassmail', get_string('settings', 'tool_sendpassmail'));

    // Add the settings page to the admin tree.
    $ADMIN->add('tools', $settings);

    // Get all course-level roles.
    $courseroles = get_roles_for_contextlevels(CONTEXT_COURSE);
    $roleoptions = [];
    
    foreach ($courseroles as $roleid) {
        $role = $DB->get_record('role', ['id' => $roleid]);
        if ($role) {
            $roleoptions[$roleid] = role_get_name($role, context_system::instance());
        }
    }

    // Sort roles by name for better UX.
    asort($roleoptions);

    // Setting for allowed roles.
    $settings->add(new admin_setting_configmulticheckbox(
        'tool_sendpassmail/allowedroles',
        get_string('allowedroles', 'tool_sendpassmail'),
        get_string('allowedroles_desc', 'tool_sendpassmail'),
        [], // Default: no roles selected.
        $roleoptions
    ));
}
