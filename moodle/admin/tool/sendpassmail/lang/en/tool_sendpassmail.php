<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Strings for the tool_sendpassmail plugin.
 *
 * @package    tool_sendpassmail
 * @copyright  2025 RAPHAEL ENES <<EMAIL>>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

$string['pluginname'] = 'Send Password Mail';
$string['invalidcourseid'] = 'Courseid Invalid';
$string['email'] = 'Email';
$string['addemail'] = 'Add Email';
$string['required'] = 'This field is required';
$string['invalidemail'] = 'Invalid email format';
$string['noemails'] = 'No emails found';
$string['action'] = 'Action';
$string['userid'] = 'User ID';
$string['courseid'] = 'Course ID';
$string['errorinsertingrecord'] = 'Error inserting record';

// Settings strings.
$string['settings'] = 'Send Password Mail Settings';
$string['allowedroles'] = 'Allowed roles';
$string['allowedroles_desc'] = 'Select which course roles can access the Send Password Mail plugin. Only users with these roles will see the plugin in course navigation.';
$string['noroleselected'] = 'No roles selected. The plugin will not be visible to any users.';
$string['privacy:metadata'] = 'The Send Password Mail plugin does not store any personal data.';

// Capability strings.
$string['sendpassmail:view'] = 'View Send Password Mail';
$string['sendpassmail:manage'] = 'Manage Send Password Mail emails';

// Privacy strings.
$string['privacy:metadata:tool_sendpassmail_emails'] = 'Information about email addresses configured for course completion notifications.';
$string['privacy:metadata:tool_sendpassmail_emails:email'] = 'The email address that will receive notifications.';
$string['privacy:metadata:tool_sendpassmail_emails:userid'] = 'The ID of the user who configured the email address.';
$string['privacy:metadata:tool_sendpassmail_emails:courseid'] = 'The ID of the course for which the email was configured.';
$string['privacy:metadata:tool_sendpassmail_emails:timecreated'] = 'The time when the email configuration was created.';